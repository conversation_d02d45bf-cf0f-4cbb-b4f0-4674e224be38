import { useState, useEffect } from 'react';
import { useAgentsApi, Agent } from 'src/services/api/use-agents-api';

// Filter options
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const CATEGORY_FILTERS = ['All', 'sales', 'marketing', 'social media'];

export const useAgentsView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  
  // Filter states
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);

  // Use the agents API hook to fetch data
  const { useGetAgentss } = useAgentsApi();
  const {
    data: agentsResponse,
    isLoading,
    error,
    refetch,
  } = useGetAgentss();

  // Extract agents from the response
  const agents = agentsResponse?.agents || [];

  // Update filtered agents when agents data changes or filters change
  useEffect(() => {
    let filtered = agents;

    // Filter by type
    if (selectedTypeFilter !== 0) {
      const typeName = TYPE_FILTERS[selectedTypeFilter];
      filtered = filtered.filter((agent) => agent.template.type === typeName);
    }

    // Filter by category
    if (selectedCategoryFilter !== 0) {
      const categoryName = CATEGORY_FILTERS[selectedCategoryFilter];
      filtered = filtered.filter((agent) => 
        agent.template.category.name.toLowerCase() === categoryName.toLowerCase()
      );
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (agent) =>
          agent.template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.template.category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          agent.specialRequest.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredAgents(filtered);
  }, [agents, searchQuery, selectedTypeFilter, selectedCategoryFilter]);

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  // Handle filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };

  return {
    // Data
    agents,
    filteredAgents,

    // Loading states
    isLoading,
    error,
    refetch,

    // Filter state
    searchQuery,
    selectedTypeFilter,
    selectedCategoryFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleTypeFilterChange,
    handleCategoryFilterChange,
  };
};

export type { Agent };
