import { useState, useEffect } from 'react';
import { useTemplatesApi, Template } from 'src/services/api/use-templates-api';

// Filter options
const VISIBILITY_FILTERS = ['Public Templates', 'Private Templates'];
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const CATEGORY_FILTERS = ['All', 'sales', 'marketing', 'social media'];

export const useAgentView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Template[]>([]);
  const [selectedVisibilityTab, setSelectedVisibilityTab] = useState(0); // 0 = Public, 1 = Private
  const [selectedTypeTab, setSelectedTypeTab] = useState(0);
  const [selectedCategoryTab, setSelectedCategoryTab] = useState(0);

  // Use the templates API hook to fetch data
  const { useGetTemplates } = useTemplatesApi();
  const {
    data: templatesResponse,
    isLoading,
    error,
    refetch,
  } = useGetTemplates();

  // Extract templates from the response
  const templates = templatesResponse?.templates || [];

  // Update filtered agents when templates data changes
  useEffect(() => {
    filterAgents(searchQuery, selectedVisibilityTab, selectedTypeTab, selectedCategoryTab);
  }, [templates, searchQuery, selectedVisibilityTab, selectedTypeTab, selectedCategoryTab]);

  // Filter agents based on search, visibility, type, and category
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAgents(query, selectedVisibilityTab, selectedTypeTab, selectedCategoryTab);
  };

  const handleVisibilityTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedVisibilityTab(newValue);
    filterAgents(searchQuery, newValue, selectedTypeTab, selectedCategoryTab);
  };

  const handleTypeTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeTab(newValue);
    filterAgents(searchQuery, selectedVisibilityTab, newValue, selectedCategoryTab);
  };

  const handleCategoryTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryTab(newValue);
    filterAgents(searchQuery, selectedVisibilityTab, selectedTypeTab, newValue);
  };

  const filterAgents = (query: string, visibilityTabIndex: number, typeTabIndex: number, categoryTabIndex: number) => {
    let filtered = templates;

    // Filter by visibility first (main tab filter)
    if (visibilityTabIndex === 0) {
      // Public Templates
      filtered = filtered.filter((agent) => agent.visibility === 'PUBLIC');
    } else {
      // Private Templates
      filtered = filtered.filter((agent) => agent.visibility === 'PRIVATE');
    }

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query.toLowerCase()) ||
          agent.description.toLowerCase().includes(query.toLowerCase()) ||
          agent.category.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by type
    if (typeTabIndex !== 0) {
      // 0 = All
      const typeName = TYPE_FILTERS[typeTabIndex];
      filtered = filtered.filter((agent) => agent.type === typeName);
    }

    // Filter by category
    if (categoryTabIndex !== 0) {
      // 0 = All
      const categoryName = CATEGORY_FILTERS[categoryTabIndex];
      filtered = filtered.filter((agent) => agent.category.name.toLowerCase() === categoryName.toLowerCase());
    }

    setFilteredAgents(filtered);
  };

  return {
    // Data
    templates,
    filteredAgents,

    // Loading states
    isLoading,
    error,
    refetch,

    // Search and filter state
    searchQuery,
    selectedVisibilityTab,
    selectedTypeTab,
    selectedCategoryTab,

    // Filter options
    VISIBILITY_FILTERS,
    TYPE_FILTERS,
    CATEGORY_FILTERS,

    // Event handlers
    handleSearch,
    handleVisibilityTabChange,
    handleTypeTabChange,
    handleCategoryTabChange,
  };
};

export type { Template };
