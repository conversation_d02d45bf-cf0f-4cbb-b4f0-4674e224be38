import { useState, useEffect } from 'react';
import { useTemplatesApi, Template, TemplateFilters } from 'src/services/api/use-templates-api';

// Filter options for table view
const TYPE_FILTERS = ['All', 'SINGLE', 'TEAM'];
const CATEGORY_FILTERS = ['All', 'sales', 'marketing', 'social media'];
const TOOLS_FILTERS = ['All', 'Web Search', 'Calculator', 'Code Interpreter'];
const MODEL_FILTERS = ['All', 'GPT-4', 'GPT-3.5', 'Claude', 'Gemini'];
const STATUS_FILTERS = ['All', 'ACTIVE', 'INACTIVE'];

export const useAgentView = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<Template[]>([]);
  const [selectedTab, setSelectedTab] = useState(0); // 0 = Cards (PUBLIC), 1 = Table (PRIVATE)
  const [selectedTypeTab, setSelectedTypeTab] = useState(0);

  // Cards view filters for public templates
  const [selectedCardsTypeFilter, setSelectedCardsTypeFilter] = useState(0);
  const [selectedCardsCategoryFilter, setSelectedCardsCategoryFilter] = useState(0);
  const [selectedCategoryTab, setSelectedCategoryTab] = useState(0);

  // Table filters for private templates
  const [selectedTypeFilter, setSelectedTypeFilter] = useState(0);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState(0);
  const [selectedToolsFilter, setSelectedToolsFilter] = useState(0);
  const [selectedModelFilter, setSelectedModelFilter] = useState(0);
  const [selectedStatusFilter, setSelectedStatusFilter] = useState(0);

  // Use the templates API hook to fetch data with visibility filter
  const { useGetTemplates } = useTemplatesApi();

  // Determine current visibility based on selected tab
  const currentVisibility = selectedTab === 0 ? 'PUBLIC' : 'PRIVATE';

  // Build filters for API call
  const apiFilters: TemplateFilters = {
    visibility: currentVisibility,
    ...(selectedTab === 0 && { // Cards view filters (public templates)
      ...(selectedCardsTypeFilter !== 0 && { type: TYPE_FILTERS[selectedCardsTypeFilter] }),
      ...(selectedCardsCategoryFilter !== 0 && { category: CATEGORY_FILTERS[selectedCardsCategoryFilter] }),
    }),
    ...(selectedTab === 1 && { // Table view filters (private templates)
      ...(selectedTypeFilter !== 0 && { type: TYPE_FILTERS[selectedTypeFilter] }),
      ...(selectedCategoryFilter !== 0 && { category: CATEGORY_FILTERS[selectedCategoryFilter] }),
      ...(selectedToolsFilter !== 0 && { tools: TOOLS_FILTERS[selectedToolsFilter] }),
      ...(selectedModelFilter !== 0 && { model: MODEL_FILTERS[selectedModelFilter] }),
      ...(selectedStatusFilter !== 0 && { status: STATUS_FILTERS[selectedStatusFilter] }),
    }),
  };

  const {
    data: templatesResponse,
    isLoading,
    error,
    refetch,
  } = useGetTemplates(apiFilters);

  // Extract templates from the response
  const templates = templatesResponse?.templates || [];
  useEffect(() => {
    filterAgents(searchQuery, selectedTypeTab, selectedCategoryTab);
  }, [templates, searchQuery, selectedTypeTab, selectedCategoryTab]);
  // Update filtered agents when templates data changes or search query changes
  useEffect(() => {
    if (selectedTab === 0) {
      // For cards view (public templates), apply search filter only (API filters are already applied)
      let filtered = templates;
      if (searchQuery) {
        filtered = filtered.filter(
          (agent) =>
            agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            agent.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
            agent.category.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      setFilteredAgents(filtered);
    } else {
      // For table view (private templates), templates are already filtered by API
      setFilteredAgents(templates);
    }
  }, [templates, searchQuery, selectedTab]);

  // Handle search for cards view
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAgents(query, selectedTypeTab, selectedCategoryTab);
  };

  const handleTypeTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeTab(newValue);
    filterAgents(searchQuery, newValue, selectedCategoryTab);
  };

  // Handle main tab change (Cards vs Table)
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
    setSearchQuery(''); // Reset search when switching tabs
    // Reset filters when switching tabs
    if (newValue === 0) {
      // Reset table filters
      setSelectedTypeFilter(0);
      setSelectedCategoryFilter(0);
      setSelectedToolsFilter(0);
      setSelectedModelFilter(0);
      setSelectedStatusFilter(0);
    } else {
      // Reset cards filters
      setSelectedCardsTypeFilter(0);
      setSelectedCardsCategoryFilter(0);
    }
  };

  // Handle cards view filter changes
  const handleCardsTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCardsTypeFilter(newValue);
  };
  const filterAgents = (query: string, typeTabIndex: number, categoryTabIndex: number) => {
    let filtered = templates;

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query.toLowerCase()) ||
          agent.description.toLowerCase().includes(query.toLowerCase()) ||
          agent.category.name.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by type
    if (typeTabIndex !== 0) {
      // 0 = All
      const typeName = TYPE_FILTERS[typeTabIndex];
      filtered = filtered.filter((agent) => agent.type === typeName);
    }

    // Filter by category
    if (categoryTabIndex !== 0) {
      // 0 = All
      const categoryName = CATEGORY_FILTERS[categoryTabIndex];
      filtered = filtered.filter((agent) => agent.category.name.toLowerCase() === categoryName.toLowerCase());
    }

    setFilteredAgents(filtered);
  };
    const handleCategoryTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryTab(newValue);
    filterAgents(searchQuery, selectedTypeTab, newValue);
  };

  const handleCardsCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCardsCategoryFilter(newValue);
  };

  // Handle table filter changes
  const handleTypeFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTypeFilter(newValue);
  };

  const handleCategoryFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryFilter(newValue);
  };

  const handleToolsFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedToolsFilter(newValue);
  };

  const handleModelFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedModelFilter(newValue);
  };

  const handleStatusFilterChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedStatusFilter(newValue);
  };



  return {
    // Data
    templates,
    filteredAgents,

    // Loading states
    isLoading,
    error,
    refetch,

    // Tab and filter state
    selectedTab,
    searchQuery,
    selectedTypeTab,
    selectedCategoryTab,

    // Cards view filters
    selectedCardsTypeFilter,
    selectedCardsCategoryFilter,

    // Table view filters
    selectedTypeFilter,
    selectedCategoryFilter,
    selectedToolsFilter,
    selectedModelFilter,
    selectedStatusFilter,

    // Filter options
    TYPE_FILTERS,
    CATEGORY_FILTERS,
    TOOLS_FILTERS,
    MODEL_FILTERS,
    STATUS_FILTERS,

    // Event handlers
    handleSearch,
    handleTabChange,

    // Cards view filter handlers
    handleCardsTypeFilterChange,
    handleCardsCategoryFilterChange,
handleCategoryTabChange,
    // Table view filter handlers
    handleTypeFilterChange,
    handleCategoryFilterChange,
    handleToolsFilterChange,
    handleModelFilterChange,
    handleStatusFilterChange,
  };
};

export type { Template };
